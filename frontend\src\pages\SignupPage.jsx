import React from 'react'
import { useState } from 'react';
import {useAuthStore}  from '../store/useAuthStore.js';
import { MessageSquare,User,Mail,Lock, Eye, EyeOff, Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import AuthImagePattern from '../components/AuthImagePattern.jsx';
import toast from 'react-hot-toast';

const SignupPage = () => {
  const[showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
  });

 const signup = useAuthStore((state) => state.signup);
 const isSigningUp = useAuthStore((state) => state.isSigningUp);


  const validateForm = ()=>{
    if(!formData.username.trim())return toast.error("Full name is required");
    if(!formData.email.trim())return toast.error("Email is required");
    if(!/\S+@\S+\.\S+/.test(formData.email))return toast.error("Invalid email format");
    if(!formData.password.trim())return toast.error("Password is required");
    if(formData.password.length<6)return toast.error("Password must be at least 6 characters long");
    return true;
  }


     

  const handleSubmit = (e) => {
    e.preventDefault();

    const success = validateForm();
    if(success===true) signup(formData);
  };


  return (
    <div className=" min-h-screen grid lg:grid-cols-2">
      {/*leftside*/}
      <div className="flex flex-col justify-center items-center p-6 sm:p-12">
        <div className="w-full max-w-sm space-y-8">
          {/*logo*/}
          <div className="text-center mb-8">
            <div className="flex flex-col items-center gap-2 group">
              <div className="size-10 rounded-xl bg-primary/10 flex justify-center items-center group-hover:bg-primary/20 transition-colors">
                <MessageSquare className="size-6 text-primary" />
              </div>
              <h1 className="text-2xl font-bold mt-2">Create Account</h1>
              <p className="text-base-content/60">
                Get started with your free account
              </p>
            </div>
          </div>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text font-bold">Full Name</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <User className="size-4 text-base-content/40" />
                </div>
                <input
                  type="text"
                  value={formData.fullName}
                  onChange={(e) =>
                    setFormData({ ...formData, username: e.target.value })
                  }
                  placeholder="Enter your full name"
                  className="input input-bordered w-full pl-10"
                />
              </div>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text font-bold">Email</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Mail className="size-4 text-base-content/40" />
                </div>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  placeholder="Enter your email"
                  className="input input-bordered w-full pl-10"
                />
              </div>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text font-bold">Password</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Lock className="size-4 text-base-content/40" />
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                  placeholder="*******"
                  className="input input-bordered w-full pl-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {
                    showPassword ?(<EyeOff className="size-4 text-base-content/40" />)
                     : ( <Eye className="size-4 text-base-content/40" />)
                  }
                </button>
              </div>
            </div>
            <button type="submit" className="btn btn-primary w-full"disabled={isSigningUp}>
              {isSigningUp ? (
                <>
                <Loader2 className="size-4 animate-spin" />
                Signing up...
                </>
                
              ) : (
                "Create Account"
              )}

            </button>
          </form>
          <div className="text-center">
            <p className='text-base-content/60'>
            Already have an account?{' '}
            <Link
            to={"/login"}
            className=" link link-primary font-bold"
            >
              Signin
            </Link>
            </p>
          </div>
        </div>
      </div>
      {/*rightside*/}
      <AuthImagePattern title="Join our community" subtitle=" Connect with friends and family" />


    </div>
  );
}

export default SignupPage